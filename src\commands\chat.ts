/**
 * Chat command implementation
 */

import { Command } from 'commander';
import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';
import boxen from 'boxen';
import { AIService } from '../services/ai.js';
import { ConfigService } from '../services/config.js';
import { logger } from '../utils/logger.js';
import { MessageInput } from '../ui/index.js';
import type { ChatMessage, CommandOptions, AIProviderConfig, SavedChatSession, MessageInputConfig } from '../types/index.js';
import { AI_PROVIDERS } from '../types/index.js';

export function createChatCommand(): Command {
  const command = new Command('chat');
  
  command
    .description('Start an interactive chat session with AI')
    .option('-p, --provider <provider>', 'AI provider to use (openai, deepseek)')
    .option('-m, --model <model>', 'Model to use')
    .option('-t, --temperature <number>', 'Temperature for responses (0-2)', parseFloat)
    .option('--max-tokens <number>', 'Maximum tokens for responses', parseInt)
    .option('-v, --verbose', 'Enable verbose logging')
    .option('-s, --system <prompt>', 'System prompt to use')
    .option('--select-provider', 'Force provider selection even if default is set')
    .option('--new-session', 'Force creation of new session instead of using saved configuration')
    .action(async (options: CommandOptions & { system?: string; selectProvider?: boolean; newSession?: boolean }) => {
      await runChatSession(options);
    });

  return command;
}

async function runChatSession(options: CommandOptions & { system?: string; selectProvider?: boolean; newSession?: boolean }): Promise<void> {
  const configService = ConfigService.getInstance();

  // Set log level if verbose
  if (options.verbose) {
    configService.updateConfig({ logLevel: 'debug' });
  }

  // Display welcome message
  displayWelcome();

  // Add a small pause to let user read the welcome message
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Get provider and model selection (with saved session support)
  const sessionConfig = await getSessionConfiguration(options);

  // Check and configure API key if needed
  const apiKeyConfigured = await ensureApiKeyConfigured(sessionConfig.provider);
  if (!apiKeyConfigured) {
    console.log(chalk.red('\nCannot start chat session without API key. Exiting...\n'));
    return;
  }

  // After API key configuration, start the chat interface
  await startChatInterface(sessionConfig, options);
}

async function startChatInterface(
  sessionConfig: {
    provider: string;
    model: string;
    temperature?: number | undefined;
    maxTokens?: number | undefined;
    systemPrompt?: string | undefined;
  },
  options: CommandOptions & { system?: string; selectProvider?: boolean; newSession?: boolean }
): Promise<void> {
  const aiService = new AIService();

  logger.info(`Starting chat session with ${sessionConfig.provider}/${sessionConfig.model}`);

  // Test connection to the provider
  console.log(chalk.cyan('\nTesting connection to AI provider...'));
  const connectionSpinner = ora('Connecting...').start();

  try {
    await aiService.testConnection({
      provider: sessionConfig.provider,
      model: sessionConfig.model,
    });
    connectionSpinner.succeed(chalk.green(`Successfully connected to ${sessionConfig.provider}`));
  } catch (error) {
    connectionSpinner.fail(chalk.red('Connection test failed'));

    if (error instanceof Error) {
      console.log(chalk.red(`\nError: ${error.message}`));

      // Provide specific guidance based on error type
      if (error.message.includes('API key')) {
        console.log(chalk.yellow('\nSuggestions:'));
        console.log(chalk.cyan('   - Check if your API key is correct'));
        console.log(chalk.cyan('   - Verify the API key has the necessary permissions'));
        console.log(chalk.cyan(`   - Try reconfiguring: npm run dev config --configure-api-key ${sessionConfig.provider}`));
      } else if (error.message.includes('connection') || error.message.includes('network')) {
        console.log(chalk.yellow('\nSuggestions:'));
        console.log(chalk.cyan('   - Check your internet connection'));
        console.log(chalk.cyan('   - Try again in a few moments'));
        console.log(chalk.cyan('   - Check if your firewall is blocking the connection'));
      }
    }

    const { shouldContinue } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'shouldContinue',
        message: 'Connection test failed. Do you want to continue anyway?',
        default: false,
      },
    ]);

    if (!shouldContinue) {
      console.log(chalk.yellow('\nChat session cancelled.'));
      return;
    }
  }

  const messages: ChatMessage[] = [];

  // Add system prompt if provided (from options or session config)
  const systemPrompt = options.system || sessionConfig.systemPrompt;
  if (systemPrompt) {
    messages.push({
      role: 'system',
      content: systemPrompt,
      timestamp: new Date(),
    });
    logger.debug('System prompt added');
  }

  console.log(chalk.green('\nChat session started! Type "exit" to quit, "clear" to clear history.\n'));

  // Initialize the modern message input component
  const messageInput = MessageInput.getInstance({
    placeholder: 'Type your message here...',
    enableHistory: true,
    showCharCount: true,
    showWordCount: false,
    theme: {
      borderColor: '#00D9FF', // Modern cyan
      focusColor: '#0099FF', // Bright blue
      textColor: '#FFFFFF', // Pure white
      placeholderColor: '#888888', // Medium gray
      errorColor: '#FF4757', // Modern red
      successColor: '#2ED573', // Modern green
      spinnerColor: '#00D9FF', // Modern cyan
      timerColor: '#FFA502', // Orange
    },
  });

  while (true) {
    try {
      // Get user input using the modern input component
      const inputResult = await messageInput.prompt('You');

      // Handle cancellation
      if (inputResult.cancelled) {
        console.log(chalk.yellow('\nGoodbye!'));
        break;
      }

      // Handle special commands
      if (inputResult.command === 'exit') {
        console.log(chalk.yellow('\nGoodbye!'));
        break;
      }

      if (inputResult.command === 'clear') {
        messages.length = 0;
        const systemPrompt = options.system || sessionConfig.systemPrompt;
        if (systemPrompt) {
          messages.push({
            role: 'system',
            content: systemPrompt,
            timestamp: new Date(),
          });
        }
        messageInput.showSuccess('Chat history cleared!');
        console.log(); // Add spacing
        continue;
      }

      const userInput = inputResult.value;

      // Add user message
      messages.push({
        role: 'user',
        content: userInput,
        timestamp: new Date(),
      });

      // Show integrated spinner with modern styling
      messageInput.startSpinner('AI is thinking...');

      try {
        // Get AI response
        const chatOptions: Partial<AIProviderConfig> = {
          provider: sessionConfig.provider,
          model: sessionConfig.model,
        };

        if (options.temperature !== undefined) {
          chatOptions.temperature = options.temperature;
        } else if (sessionConfig.temperature !== undefined) {
          chatOptions.temperature = sessionConfig.temperature;
        }

        if (options.maxTokens !== undefined) {
          chatOptions.maxTokens = options.maxTokens;
        } else if (sessionConfig.maxTokens !== undefined) {
          chatOptions.maxTokens = sessionConfig.maxTokens;
        }

        const response = await aiService.chat(messages, chatOptions);

        messageInput.stopSpinner();

        // Add AI response to messages
        messages.push({
          role: 'assistant',
          content: response.content,
          timestamp: new Date(),
        });

        // Display response with modern styling that matches our input component
        console.log(); // Add spacing before response

        const responseBox = boxen(response.content, {
          padding: 1,
          margin: { top: 0, bottom: 1, left: 0, right: 0 },
          borderStyle: 'round',
          borderColor: 'cyan',
          title: 'AI Response',
          titleAlignment: 'left',
          width: process.stdout.columns || 80,
        });

        console.log(responseBox);

        // Show usage info if available
        if (response.usage && options.verbose) {
          logger.debug(`Tokens used: ${response.usage.totalTokens} (prompt: ${response.usage.promptTokens}, completion: ${response.usage.completionTokens})`);
        }

      } catch (error) {
        messageInput.stopSpinner();
        logger.error('Failed to get AI response:', error);

        if (error instanceof Error) {
          // Use the modern input component for error display
          messageInput.showError(`Error: ${error.message}`);

          // Provide actionable suggestions based on error type
          if (error.message.includes('API key') || error.message.includes('Authentication')) {
            console.log(chalk.yellow('\nSuggestions:'));
            console.log(chalk.cyan('   - Check your API key configuration'));
            console.log(chalk.cyan(`   - Reconfigure: npm run dev config --configure-api-key ${sessionConfig.provider}`));
          } else if (error.message.includes('connection') || error.message.includes('timeout')) {
            console.log(chalk.yellow('\nSuggestions:'));
            console.log(chalk.cyan('   - Check your internet connection'));
            console.log(chalk.cyan('   - Try again in a moment'));
          } else if (error.message.includes('rate limit')) {
            console.log(chalk.yellow('\nSuggestion:'));
            console.log(chalk.cyan('   - Wait a moment before sending another message'));
          }

          console.log(); // Add spacing
        }
      }

    } catch (error) {
      if (error instanceof Error && error.message.includes('User force closed')) {
        console.log(chalk.yellow('\nGoodbye!'));
        break;
      }
      logger.error('Unexpected error:', error);
    }
  }
}

async function getSessionConfiguration(options: CommandOptions & { selectProvider?: boolean; newSession?: boolean; system?: string }): Promise<{
  provider: string;
  model: string;
  temperature?: number | undefined;
  maxTokens?: number | undefined;
  systemPrompt?: string | undefined;
}> {
  const configService = ConfigService.getInstance();
  const config = configService.getConfig();

  // Check if we should offer saved sessions
  const hasValidSessions = configService.hasValidSavedSessions();
  const lastUsedSession = configService.getLastUsedSession();

  // If user explicitly wants new session or no provider specified, or no valid saved sessions
  const shouldPromptForSession = !options.provider && !options.newSession && hasValidSessions;

  if (shouldPromptForSession) {
    console.log(chalk.cyan('\nSession Configuration Options:\n'));

    const sessionChoices = [];

    // Add option to use last used session if available
    if (lastUsedSession && configService.getApiKey(lastUsedSession.provider)) {
      sessionChoices.push({
        name: `Continue with last session: ${lastUsedSession.name} (${lastUsedSession.provider}/${lastUsedSession.model})`,
        value: 'last_session',
      });
    }

    // Add saved sessions
    const savedSessions = configService.getSavedSessions().slice(0, 5); // Show top 5 recent sessions
    savedSessions.forEach(session => {
      if (configService.getApiKey(session.provider)) {
        sessionChoices.push({
          name: `${session.name} (${session.provider}/${session.model}) - ${new Date(session.lastUsed).toLocaleDateString()}`,
          value: session.id,
        });
      }
    });

    // Add option to create new session
    sessionChoices.push({
      name: 'Create new session configuration',
      value: 'new_session',
    });

    const { sessionChoice } = await inquirer.prompt([
      {
        type: 'list',
        name: 'sessionChoice',
        message: 'Choose session configuration:',
        choices: sessionChoices,
        pageSize: 10,
      },
    ]);

    if (sessionChoice === 'last_session' && lastUsedSession) {
      configService.updateLastUsedSession(lastUsedSession.id);
      console.log(chalk.green(`Using last session: ${lastUsedSession.name}\n`));
      return {
        provider: lastUsedSession.provider,
        model: lastUsedSession.model,
        temperature: lastUsedSession.temperature,
        maxTokens: lastUsedSession.maxTokens,
        systemPrompt: lastUsedSession.systemPrompt,
      };
    } else if (sessionChoice !== 'new_session') {
      const selectedSession = configService.getSavedSessions().find(s => s.id === sessionChoice);
      if (selectedSession) {
        configService.updateLastUsedSession(selectedSession.id);
        console.log(chalk.green(`Using saved session: ${selectedSession.name}\n`));
        return {
          provider: selectedSession.provider,
          model: selectedSession.model,
          temperature: selectedSession.temperature,
          maxTokens: selectedSession.maxTokens,
          systemPrompt: selectedSession.systemPrompt,
        };
      }
    }
  }

  // Create new session configuration
  return await createNewSessionConfiguration(options, config);
}

async function createNewSessionConfiguration(
  options: CommandOptions & { selectProvider?: boolean; system?: string },
  config: any
): Promise<{
  provider: string;
  model: string;
  temperature?: number | undefined;
  maxTokens?: number | undefined;
  systemPrompt?: string | undefined;
}> {
  let provider: string;
  let model: string;

  // If provider not specified via command line OR user wants to select, prompt user
  if (!options.provider || options.selectProvider) {
    console.log(chalk.cyan('\nPlease select your AI provider:\n'));

    const { selectedProvider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedProvider',
        message: 'Choose AI provider:',
        choices: Object.entries(AI_PROVIDERS).map(([key, value]) => ({
          name: `${value.name} - ${value.description}`,
          value: key,
        })),
        default: config.defaultProvider,
        pageSize: 10,
      },
    ]);
    provider = selectedProvider;
    console.log(chalk.green(`Selected provider: ${AI_PROVIDERS[provider]?.name}\n`));
  } else {
    provider = options.provider;
  }

  // If model not specified via command line, prompt user
  if (!options.model) {
    const providerInfo = AI_PROVIDERS[provider];
    if (providerInfo && providerInfo.models.length > 1) {
      console.log(chalk.cyan(`\nAvailable models for ${providerInfo.name}:\n`));

      const { selectedModel } = await inquirer.prompt([
        {
          type: 'list',
          name: 'selectedModel',
          message: 'Choose model:',
          choices: providerInfo.models,
          default: providerInfo.models[0],
          pageSize: 10,
        },
      ]);
      model = selectedModel;
      console.log(chalk.green(`Selected model: ${model}\n`));
    } else if (providerInfo) {
      model = providerInfo.models[0] || config.defaultModel;
      console.log(chalk.yellow(`Using default model: ${model}\n`));
    } else {
      model = config.defaultModel;
      console.log(chalk.yellow(`Using fallback model: ${model}\n`));
    }
  } else {
    model = options.model;
  }

  // Ask if user wants to save this configuration
  const { shouldSave } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'shouldSave',
      message: 'Save this configuration for future use?',
      default: true,
    },
  ]);

  const sessionConfig = {
    provider,
    model,
    temperature: options.temperature,
    maxTokens: options.maxTokens,
    systemPrompt: options.system,
  };

  if (shouldSave) {
    const configService = ConfigService.getInstance();
    const savedSession = configService.saveChatSession(sessionConfig);
    console.log(chalk.green(`Configuration saved as: ${savedSession.name}\n`));
  }

  return sessionConfig;
}

function displayWelcome(): void {
  const welcome = boxen(
    chalk.bold.cyan('Welcome to Arien AI CLI\n') +
    chalk.white('A modern terminal interface powered by AI\n\n') +
    chalk.gray('Supported providers: OpenAI, DeepSeek\n') +
    chalk.yellow('Use ↑↓ arrow keys to navigate, Enter to select'),
    {
      padding: 1,
      margin: 1,
      borderStyle: 'double',
      borderColor: 'cyan',
      textAlignment: 'center',
    }
  );

  console.log(welcome);
}

/**
 * Ensure API key is configured for the selected provider
 */
async function ensureApiKeyConfigured(provider: string): Promise<boolean> {
  const configService = ConfigService.getInstance();
  const providerInfo = AI_PROVIDERS[provider];

  if (!providerInfo) {
    console.log(chalk.red(`Unknown provider: ${provider}`));
    return false;
  }

  // Check if API key is already available
  let apiKey = configService.getApiKey(provider);

  if (apiKey) {
    console.log(chalk.green(`API key found for ${providerInfo.name}\n`));
    return true;
  }

  // API key not found, prompt user to configure it
  console.log(chalk.yellow(`\nNo API key found for ${providerInfo.name}`));
  console.log(chalk.gray(`Environment variable: ${providerInfo.apiKeyEnvVar}`));

  const { shouldConfigure } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'shouldConfigure',
      message: `Would you like to configure the API key for ${providerInfo.name} now?`,
      default: true,
    },
  ]);

  if (!shouldConfigure) {
    console.log(chalk.yellow('\nYou can set the API key later using:'));
    console.log(chalk.cyan(`   export ${providerInfo.apiKeyEnvVar}=your_api_key_here`));
    console.log(chalk.cyan(`   or use: npm run dev config --configure-api-key ${provider}\n`));
    return false;
  }

  // Prompt for API key
  const { newApiKey } = await inquirer.prompt([
    {
      type: 'password',
      name: 'newApiKey',
      message: `Enter your ${providerInfo.name} API key:`,
      mask: '*',
      validate: (input: string) => {
        if (!input || input.trim().length === 0) {
          return 'API key cannot be empty';
        }
        if (input.trim().length < 10) {
          return 'API key seems too short. Please check and try again.';
        }
        return true;
      },
    },
  ]);

  // Save the API key to config
  const currentProviderConfig = configService.getProviderConfig(provider) || {
    provider,
    model: providerInfo.models[0] || 'default',
    apiKey: '',
  };

  currentProviderConfig.apiKey = newApiKey.trim();
  configService.setProviderConfig(provider, currentProviderConfig);

  console.log(chalk.green(`\nAPI key configured successfully for ${providerInfo.name}!`));
  console.log(chalk.cyan('Starting fresh chat session...\n'));

  // Add a small delay for better UX
  await new Promise(resolve => setTimeout(resolve, 1500));

  // Clear the screen for a fresh start
  console.clear();

  return true;
}
