/**
 * Modern Message Input Component
 * A sophisticated input component with enhanced styling and features for the chat interface
 */

import chalk from 'chalk';
import inquirer from 'inquirer';
import { stdout } from 'process';
import type { MessageInputConfig, MessageInputResult, InputHistoryItem, SpinnerState } from '../../types/index.js';

export class MessageInput {
  private config: Required<MessageInputConfig>;
  private history: InputHistoryItem[];
  private static instance: MessageInput | null = null;
  private spinnerState: SpinnerState | null = null;
  private spinnerInterval: NodeJS.Timeout | null = null;

  constructor(config: MessageInputConfig = {}) {
    this.config = {
      placeholder: config.placeholder || 'Type your message...',
      multiline: config.multiline ?? false, // Keep simple for now
      maxLines: config.maxLines || 5,
      showCharCount: config.showCharCount ?? false,
      showWordCount: config.showWordCount ?? false,
      enableHistory: config.enableHistory ?? true,
      enableAutoComplete: config.enableAutoComplete ?? false, // Keep simple for now
      theme: {
        borderColor: '#00D9FF', // Modern cyan
        focusColor: '#0099FF', // Bright blue
        textColor: '#FFFFFF', // Pure white
        placeholderColor: '#888888', // Medium gray
        counterColor: '#FFD700', // Gold
        errorColor: '#FF4757', // Modern red
        successColor: '#2ED573', // Modern green
        spinnerColor: '#00D9FF', // Modern cyan
        timerColor: '#FFA502', // Orange
        ...config.theme,
      },
    };

    this.history = [];
  }

  /**
   * Get singleton instance
   */
  static getInstance(config?: MessageInputConfig): MessageInput {
    if (!MessageInput.instance) {
      MessageInput.instance = new MessageInput(config);
    }
    return MessageInput.instance;
  }

  /**
   * Get terminal width with fallback
   */
  private getTerminalWidth(): number {
    return stdout.columns || process.stdout.columns || 80;
  }

  /**
   * Display the modern input prompt and wait for user input
   */
  async prompt(message: string = 'You:'): Promise<MessageInputResult> {
    // Display the modern input header
    this.displayInputHeader(message);

    try {
      const { userInput } = await inquirer.prompt({
        type: 'input',
        name: 'userInput',
        message: this.createStyledPrompt(),
        validate: (input: string) => {
          const trimmed = input.trim();
          if (trimmed.length === 0) {
            return chalk.hex(this.config.theme.errorColor!)('Please enter a message');
          }
          if (trimmed.length > 4000) {
            return chalk.hex(this.config.theme.errorColor!)('Message too long (max 4000 characters)');
          }
          return true;
        },
        transformer: (input: string, { isFinal }: { isFinal: boolean }) => {
          if (!isFinal && input.length === 0) {
            return chalk.hex(this.config.theme.placeholderColor!)(this.config.placeholder);
          }

          // Add character count for long messages
          const styledInput = chalk.hex(this.config.theme.textColor!)(input);
          if (input.length > 100) {
            const count = chalk.hex(this.config.theme.counterColor!)(`(${input.length}/4000)`);
            return `${styledInput} ${count}`;
          }

          return styledInput;
        },
      });

      const trimmedInput = userInput.trim();

      // Handle special commands
      if (trimmedInput.toLowerCase() === 'exit') {
        this.displayInputFooter();
        return { value: trimmedInput, command: 'exit' };
      }

      if (trimmedInput.toLowerCase() === 'clear') {
        this.displayInputFooter();
        return { value: trimmedInput, command: 'clear' };
      }

      // Add to history
      if (this.config.enableHistory) {
        this.addToHistory(trimmedInput);
      }

      this.displayInputFooter();
      return { value: trimmedInput };

    } catch (error) {
      this.displayInputFooter();
      return { value: '', cancelled: true };
    }
  }

  /**
   * Display the modern input header with styling
   */
  private displayInputHeader(message: string): void {
    const theme = this.config.theme;
    const terminalWidth = this.getTerminalWidth();
    const boxWidth = Math.min(terminalWidth - 4, 80); // Max width of 80, with margin

    console.log(); // Add spacing before input box

    // Create top border with title
    const title = `${message}`;
    const topBorder = this.createBorderLine('top', boxWidth, theme.borderColor!, title);
    console.log(topBorder);

    // Create instruction line
    const instructionText = 'Type your message below (or "exit" to quit, "clear" to reset)';
    const instructionLine = this.createContentLine(
      instructionText,
      boxWidth,
      theme.borderColor!,
      theme.placeholderColor!,
      'center'
    );
    console.log(instructionLine);

    // Create separator
    const separator = this.createBorderLine('separator', boxWidth, theme.borderColor!);
    console.log(separator);
  }

  /**
   * Create styled prompt message
   */
  private createStyledPrompt(): string {
    const theme = this.config.theme;
    return chalk.hex(theme.borderColor!)('│ ') +
           chalk.hex(theme.focusColor!)('> ') +
           chalk.hex(theme.textColor!)('Message:');
  }

  /**
   * Display the input footer
   */
  private displayInputFooter(): void {
    const theme = this.config.theme;
    const terminalWidth = this.getTerminalWidth();
    const boxWidth = Math.min(terminalWidth - 4, 80);

    // Create bottom border
    const bottomBorder = this.createBorderLine('bottom', boxWidth, theme.borderColor!);
    console.log(bottomBorder);
    console.log(); // Add spacing after input box
  }

  /**
   * Get spinner animation frames (modern ball animation)
   */
  private getSpinnerFrames(): string[] {
    return ['●', '○', '◐', '◓', '◑', '◒'];
  }

  /**
   * Start the integrated spinner animation
   */
  startSpinner(message: string = 'Processing'): void {
    if (this.spinnerState) {
      this.stopSpinner();
    }

    this.spinnerState = {
      isActive: true,
      startTime: new Date(),
      frameIndex: 0,
      message,
    };

    this.spinnerInterval = setInterval(() => {
      this.updateSpinnerDisplay();
    }, 150); // Update every 150ms for smooth animation

    // Initial display
    this.updateSpinnerDisplay();
  }

  /**
   * Stop the spinner animation
   */
  stopSpinner(): void {
    if (this.spinnerInterval) {
      clearInterval(this.spinnerInterval);
      this.spinnerInterval = null;
    }

    if (this.spinnerState) {
      // Clear the spinner line
      process.stdout.write('\r\x1b[K');
      this.spinnerState = null;
    }
  }

  /**
   * Update the spinner display with animation and elapsed time
   */
  private updateSpinnerDisplay(): void {
    if (!this.spinnerState) return;

    const theme = this.config.theme;
    const terminalWidth = this.getTerminalWidth();
    const frames = this.getSpinnerFrames();
    const currentFrame = frames[this.spinnerState.frameIndex % frames.length];

    // Calculate elapsed time
    const elapsed = Math.floor((Date.now() - this.spinnerState.startTime.getTime()) / 1000);
    const elapsedText = `${elapsed}s`;

    // Create spinner line
    const spinnerText = `${currentFrame} ${this.spinnerState.message}`;
    const contentWidth = terminalWidth - 2; // Account for borders
    const availableSpace = contentWidth - spinnerText.length - elapsedText.length - 2; // 2 for spacing
    const padding = Math.max(0, availableSpace);

    const spinnerLine = chalk.hex(theme.borderColor!)('│ ') +
                       chalk.hex(theme.spinnerColor!)(currentFrame) + ' ' +
                       chalk.hex(theme.textColor!)(this.spinnerState.message) +
                       ' '.repeat(padding) +
                       chalk.hex(theme.timerColor!)(elapsedText) +
                       chalk.hex(theme.borderColor!)(' │');

    // Update display
    process.stdout.write('\r' + spinnerLine);

    // Increment frame index
    this.spinnerState.frameIndex++;
  }

  /**
   * Add input to history
   */
  private addToHistory(value: string): void {
    // Don't add empty values or duplicates
    if (value.trim().length === 0) return;

    const lastItem = this.history[this.history.length - 1];
    if (lastItem && lastItem.value === value) return;

    this.history.push({
      value,
      timestamp: new Date(),
    });

    // Keep history size manageable (last 100 items)
    if (this.history.length > 100) {
      this.history = this.history.slice(-100);
    }
  }

  /**
   * Show error message with modern styling
   */
  showError(message: string): void {
    const theme = this.config.theme;
    const terminalWidth = this.getTerminalWidth();
    const boxWidth = Math.min(terminalWidth - 4, 80);

    console.log(); // Add spacing

    // Create error box
    const topBorder = this.createBorderLine('top', boxWidth, theme.errorColor!, 'Error');
    const errorContent = this.createContentLine(message, boxWidth, theme.errorColor!, theme.errorColor!, 'left');
    const bottomBorder = this.createBorderLine('bottom', boxWidth, theme.errorColor!);

    console.log(topBorder);
    console.log(errorContent);
    console.log(bottomBorder);
    console.log(); // Add spacing
  }

  /**
   * Show success message with modern styling
   */
  showSuccess(message: string): void {
    const theme = this.config.theme;
    const terminalWidth = this.getTerminalWidth();
    const boxWidth = Math.min(terminalWidth - 4, 80);

    console.log(); // Add spacing

    // Create success box
    const topBorder = this.createBorderLine('top', boxWidth, theme.successColor!, 'Success');
    const successContent = this.createContentLine(message, boxWidth, theme.successColor!, theme.successColor!, 'left');
    const bottomBorder = this.createBorderLine('bottom', boxWidth, theme.successColor!);

    console.log(topBorder);
    console.log(successContent);
    console.log(bottomBorder);
    console.log(); // Add spacing
  }

  /**
   * Get input history
   */
  getHistory(): InputHistoryItem[] {
    return [...this.history];
  }

  /**
   * Clear input history
   */
  clearHistory(): void {
    this.history = [];
  }

  /**
   * Set custom theme
   */
  setTheme(theme: Partial<MessageInputConfig['theme']>): void {
    this.config.theme = { ...this.config.theme, ...theme };
  }

  /**
   * Get the last N history items
   */
  getRecentHistory(count: number = 10): InputHistoryItem[] {
    return this.history.slice(-count);
  }

  /**
   * Check if history is enabled
   */
  isHistoryEnabled(): boolean {
    return this.config.enableHistory;
  }

  /**
   * Get current configuration
   */
  getConfig(): Required<MessageInputConfig> {
    return { ...this.config };
  }

  /**
   * Create a styled border line for the input box
   */
  private createBorderLine(
    type: 'top' | 'middle' | 'bottom' | 'separator',
    width: number,
    color: string,
    title?: string
  ): string {
    const borderChar = '─';
    let leftChar: string;
    let rightChar: string;

    switch (type) {
      case 'top':
        leftChar = '╭';
        rightChar = '╮';
        break;
      case 'middle':
        leftChar = '├';
        rightChar = '┤';
        break;
      case 'bottom':
        leftChar = '╰';
        rightChar = '╯';
        break;
      case 'separator':
        leftChar = '├';
        rightChar = '┤';
        break;
    }

    if (title && type === 'top') {
      const titleLength = title.length + 2; // Add padding
      const remainingWidth = Math.max(0, width - titleLength - 2);
      const leftPadding = Math.floor(remainingWidth / 2);
      const rightPadding = remainingWidth - leftPadding;

      return chalk.hex(color)(
        leftChar +
        borderChar.repeat(leftPadding) +
        ` ${title} ` +
        borderChar.repeat(rightPadding) +
        rightChar
      );
    }

    return chalk.hex(color)(leftChar + borderChar.repeat(width - 2) + rightChar);
  }

  /**
   * Create a styled content line for the input box
   */
  private createContentLine(
    content: string,
    width: number,
    borderColor: string,
    contentColor?: string,
    alignment: 'left' | 'center' | 'right' = 'left'
  ): string {
    const maxContentWidth = width - 4; // Account for borders and padding
    let paddedContent = content;

    // Truncate if too long
    if (content.length > maxContentWidth) {
      paddedContent = content.substring(0, maxContentWidth - 3) + '...';
    }

    // Apply alignment
    const padding = maxContentWidth - paddedContent.length;
    switch (alignment) {
      case 'center':
        const leftPad = Math.floor(padding / 2);
        const rightPad = padding - leftPad;
        paddedContent = ' '.repeat(leftPad) + paddedContent + ' '.repeat(rightPad);
        break;
      case 'right':
        paddedContent = ' '.repeat(padding) + paddedContent;
        break;
      case 'left':
      default:
        paddedContent = paddedContent + ' '.repeat(padding);
        break;
    }

    const styledContent = contentColor
      ? chalk.hex(contentColor)(paddedContent)
      : paddedContent;

    return chalk.hex(borderColor)('│ ') + styledContent + chalk.hex(borderColor)(' │');
  }
}
