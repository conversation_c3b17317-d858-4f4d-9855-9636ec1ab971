#!/usr/bin/env node

/**
 * Arien AI CLI - Modern terminal interface powered by AI
 * 
 * A TypeScript-based CLI tool that provides multi-provider AI chat capabilities
 * with support for OpenAI, DeepSeek, and other OpenAI-compatible providers.
 */

import { Command } from 'commander';
import chalk from 'chalk';
import figlet from 'figlet';
import inquirer from 'inquirer';
import { createChatCommand } from './commands/chat.js';
import { ConfigService } from './services/config.js';
import { logger } from './utils/logger.js';
import { AI_PROVIDERS } from './types/index.js';

const program = new Command();

async function main(): Promise<void> {
  try {
    // Initialize configuration service
    const configService = ConfigService.getInstance();
    
    // Display ASCII art banner
    console.log(
      chalk.cyan(
        figlet.textSync('Arien AI', {
          font: 'Standard',
          horizontalLayout: 'default',
          verticalLayout: 'default',
        })
      )
    );

    // Configure main program
    program
      .name('arien')
      .description('Modern CLI terminal system powered by AI with multi-provider support')
      .version('1.0.0')
      .configureOutput({
        writeErr: (str) => process.stderr.write(chalk.red(str)),
        writeOut: (str) => process.stdout.write(str),
      });

    // Add chat command
    program.addCommand(createChatCommand());

    // Add config command
    const configCommand = new Command('config');
    configCommand
      .description('Manage configuration settings')
      .option('--show', 'Show current configuration')
      .option('--reset', 'Reset configuration to defaults')
      .option('--set-provider <provider>', 'Set default provider')
      .option('--set-model <model>', 'Set default model')
      .option('--configure-api-key <provider>', 'Configure API key for provider interactively')
      .option('--clear-sessions', 'Clear all saved sessions')
      .action(async (options) => {
        await handleConfigCommand(options);
      });

    program.addCommand(configCommand);

    // Add providers command
    const providersCommand = new Command('providers');
    providersCommand
      .description('List available AI providers')
      .action(() => {
        console.log(chalk.bold('\nAvailable AI Providers:\n'));

        Object.entries(AI_PROVIDERS).forEach(([key, provider]) => {
          console.log(chalk.cyan(`${provider.name} (${key})`));
          console.log(chalk.gray(`  Description: ${provider.description}`));
          console.log(chalk.gray(`  Base URL: ${provider.baseURL}`));
          console.log(chalk.gray(`  Models: ${provider.models.join(', ')}`));
          console.log(chalk.gray(`  API Key Env: ${provider.apiKeyEnvVar}`));
          console.log();
        });
      });

    program.addCommand(providersCommand);

    // Parse command line arguments
    await program.parseAsync();

  } catch (error) {
    logger.error('Application error:', error);
    process.exit(1);
  }
}

async function handleConfigCommand(options: {
  show?: boolean;
  reset?: boolean;
  setProvider?: string;
  setModel?: string;
  configureApiKey?: string;
  clearSessions?: boolean;
}): Promise<void> {
  const configService = ConfigService.getInstance();

  if (options.show) {
    const config = configService.getConfig();
    console.log(chalk.bold('\nCurrent Configuration:\n'));
    console.log(chalk.cyan('Default Provider:'), config.defaultProvider);
    console.log(chalk.cyan('Default Model:'), config.defaultModel);
    console.log(chalk.cyan('Temperature:'), config.temperature);
    console.log(chalk.cyan('Max Tokens:'), config.maxTokens);
    console.log(chalk.cyan('Log Level:'), config.logLevel);

    console.log(chalk.bold('\nProvider Configurations:'));
    Object.entries(config.providers).forEach(([provider, providerConfig]) => {
      console.log(chalk.yellow(`  ${provider}:`));
      console.log(chalk.gray(`    Model: ${providerConfig.model}`));
      console.log(chalk.gray(`    Base URL: ${providerConfig.baseURL || 'default'}`));
      console.log(chalk.gray(`    API Key: ${providerConfig.apiKey ? '***configured***' : 'not set'}`));
    });

    console.log(chalk.bold('\nSaved Sessions:'));
    const savedSessions = configService.getSavedSessions();
    if (savedSessions.length === 0) {
      console.log(chalk.gray('  No saved sessions'));
    } else {
      savedSessions.slice(0, 10).forEach(session => {
        const isLastUsed = config.lastUsedSession?.id === session.id;
        const marker = isLastUsed ? chalk.green('[ACTIVE] ') : '         ';
        console.log(`${marker}${chalk.yellow(session.name)}`);
        console.log(chalk.gray(`         Provider: ${session.provider}, Model: ${session.model}`));
        console.log(chalk.gray(`         Last used: ${new Date(session.lastUsed).toLocaleString()}`));
        if (session.systemPrompt) {
          console.log(chalk.gray(`         System prompt: ${session.systemPrompt.substring(0, 50)}${session.systemPrompt.length > 50 ? '...' : ''}`));
        }
      });
    }
    console.log();
    return;
  }

  if (options.reset) {
    configService.resetConfig();
    logger.success('Configuration reset to defaults');
    return;
  }

  if (options.setProvider) {
    if (!AI_PROVIDERS[options.setProvider]) {
      logger.error(`Unknown provider: ${options.setProvider}`);
      return;
    }
    configService.updateConfig({ defaultProvider: options.setProvider });
    logger.success(`Default provider set to: ${options.setProvider}`);
    return;
  }

  if (options.setModel) {
    configService.updateConfig({ defaultModel: options.setModel });
    logger.success(`Default model set to: ${options.setModel}`);
    return;
  }

  if (options.configureApiKey) {
    await configureApiKeyInteractively(options.configureApiKey);
    return;
  }

  if (options.clearSessions) {
    const { confirmClear } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirmClear',
        message: 'Are you sure you want to clear all saved sessions?',
        default: false,
      },
    ]);

    if (confirmClear) {
      configService.updateConfig({ savedSessions: {}, lastUsedSession: undefined });
      logger.success('All saved sessions cleared');
    } else {
      console.log(chalk.yellow('Operation cancelled'));
    }
    return;
  }

  // If no specific action, show help
  console.log(chalk.yellow('Use --show to view current configuration'));
}

/**
 * Configure API key for a provider interactively
 */
async function configureApiKeyInteractively(provider: string): Promise<void> {
  const configService = ConfigService.getInstance();
  const providerInfo = AI_PROVIDERS[provider];

  if (!providerInfo) {
    logger.error(`Unknown provider: ${provider}`);
    console.log(chalk.cyan('Available providers:'), Object.keys(AI_PROVIDERS).join(', '));
    return;
  }

  console.log(chalk.bold(`\nConfiguring API key for ${providerInfo.name}\n`));
  console.log(chalk.gray(`Environment variable: ${providerInfo.apiKeyEnvVar}`));
  console.log(chalk.gray(`Description: ${providerInfo.description}\n`));

  // Check if API key already exists
  const existingKey = configService.getApiKey(provider);
  if (existingKey) {
    const { shouldOverwrite } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'shouldOverwrite',
        message: `API key already configured for ${providerInfo.name}. Overwrite?`,
        default: false,
      },
    ]);

    if (!shouldOverwrite) {
      console.log(chalk.yellow('API key configuration cancelled.'));
      return;
    }
  }

  // Prompt for new API key
  const { newApiKey } = await inquirer.prompt([
    {
      type: 'password',
      name: 'newApiKey',
      message: `Enter your ${providerInfo.name} API key:`,
      mask: '*',
      validate: (input: string) => {
        if (!input || input.trim().length === 0) {
          return 'API key cannot be empty';
        }
        if (input.trim().length < 10) {
          return 'API key seems too short. Please check and try again.';
        }
        return true;
      },
    },
  ]);

  // Save the API key
  const currentProviderConfig = configService.getProviderConfig(provider) || {
    provider,
    model: providerInfo.models[0] || 'default',
    apiKey: '',
  };

  currentProviderConfig.apiKey = newApiKey.trim();
  configService.setProviderConfig(provider, currentProviderConfig);

  logger.success(`API key configured successfully for ${providerInfo.name}!`);
  console.log(chalk.cyan(`\nYou can now use: npm run dev chat --provider ${provider}\n`));
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Start the application
main().catch((error) => {
  logger.error('Failed to start application:', error);
  process.exit(1);
});
